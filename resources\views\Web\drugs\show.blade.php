@extends('layouts.app')

@section('title', 'Drug Details')

@section('content')
    <div class="d-flex flex-column flex-column-fluid">
        <div id="kt_app_content" class="app-content flex-column-fluid">
            <div id="kt_app_content_container" class="app-container container-fluid">
                <!-- Page Header -->
                <div class="row mt-5 mb-5">
                    <div class="col-12">
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">Drug Details<span class="page-desc text-muted fs-7 fw-semibold pt-1">View comprehensive drug information</span></h1>
                            </div>
                            <div>
                                <a href="{{ route('drugs.index') }}" class="btn btn-secondary"><i class="fas fa-arrow-left"></i> Back to Drugs</a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Main Content -->
                <div class="row g-5 g-xl-10">
                    <div class="col-xl-12">
                        <div class="card card-flush">
                            <div class="card-body pt-0">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="fv-row">
                                            <label for="generic_name" class="form-label fw-semibold fs-6">Generic Name</label>
                                            <p class="form-control-plaintext">{{ $drug->generic_name }}</p>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="fv-row">
                                            <label for="brand_name" class="form-label fw-semibold fs-6">Brand Name</label>
                                            <p class="form-control-plaintext">{{ $drug->brand_name }}</p>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="fv-row">
                                            <label for="iupac_name" class="form-label fw-semibold fs-6">IUPAC Name</label>
                                            <p class="form-control-plaintext">{{ $drug->iupac_name }}</p>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="fv-row">
                                            <label for="status" class="form-label fw-semibold fs-6">Status</label>
                                            <p class="form-control-plaintext">{{ $drug->status == 1 ? 'Active' : 'Inactive' }}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
